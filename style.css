body {
    margin: 0;
    overscroll-behavior: none;
    overflow: hidden;
    background-color: #000011;
    color: #DDD;
    font-family: Helvetica;
}

#title {
    padding: 10px 0 10px 0;
    text-align: center;
}

.wrapper {
    position: relative;
}

svg {
    display: block;
    width: 100%;
    height: 100vh;
    position: fixed;
    z-index: 3;
    top: 0;
    left: 0;
}

/* 3D Neural Network Graph */
#brain {
    opacity: 0.8;
    transform-origin: center center;
}

#3d-graph {
    width: 200px !important;
    height: 200px !important;
    border-radius: 50%;
    overflow: hidden;
}

#3d-graph canvas {
    border-radius: 50%;
}

.scrollElement {
    position: absolute;
    height: 6000px;
    width: 100%;
    top: 0;
    z-index: 4;
}

.btn {
    position: fixed;
    bottom: 5%;
    right: 0px;
    transform: translateX(-50%);
    border: 1px solid #fff;
    border-radius: 5px;
    font-size: 0.9rem;
    padding: 0.5rem 0.7em;
    background-color: transparent;
    color: #ffffff;
    font-family: Verdana, Geneva, Tahoma, sans-serif;
    -webkit-font-smoothing: antialiased;
    cursor: pointer;
    transition: all .3s;
    z-index: 11;
}

.btn_works {
    left: 100px;
    right: unset;
    text-decoration: none;
}

.btn:hover {
    background: #ffffff;
    color: #1B1734;
}

/* MARK: ---0-490 Mobile */
@media (max-width: 490px) {

    #text,
    #arrow {
        transform-origin: 50% 50%;
        transform: translateY(-120px) scale(0.8);
    }

    #info2 {
        transform-origin: 50% 50%;
        transform: translateY(-120px) scale(0.8);
    }
}


#starCanvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: 10; /* Increased to ensure it's above everything */
    pointer-events: none;
    opacity: 1; /* Start hidden */
    transition: opacity 1s ease;
}

/* When active */
.starCanvas-active {
    opacity: 1 !important;
}