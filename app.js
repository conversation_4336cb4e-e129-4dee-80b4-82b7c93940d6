// More info about this project and the newer optimized version: https://isladjan.com/work/4/
// Author: isladjan - https://isladjan.com/

gsap.registerPlugin(ScrollTrigger);

let speed = 100;
let height = document.querySelector("svg").getBBox().height;

// Initialize canvas for interactive stars
const starCanvas = document.createElement('canvas');
starCanvas.id = 'starCanvas';
document.body.appendChild(starCanvas);

// Set canvas styles
starCanvas.style.position = 'fixed';
starCanvas.style.top = '0';
starCanvas.style.left = '0';
starCanvas.style.width = '100%';
starCanvas.style.height = '100vh';
starCanvas.style.zIndex = '2';
starCanvas.style.pointerEvents = 'none';
starCanvas.style.opacity = '0';
starCanvas.style.transition = 'opacity 1s ease';

gsap.set("#h2-1", { opacity: 0 });
gsap.set("#bg_grad", { attr: { cy: "-50" } });
gsap.set(["#dinoL", "#dinoR"], { y: 80 });
gsap.set("#dinoL", { x: -10 });

const mm = gsap.matchMedia();
mm.add("(max-width: 1922px)", () => {
    gsap.set(["#cloudStart-L", "#cloudStart-R"], { x: 10, opacity: 1 });
});

/* SCENE 1 */
let scene1 = gsap.timeline();
ScrollTrigger.create({
    animation: scene1,
    trigger: ".scrollElement",
    start: "top top",
    end: "45% 100%",
    scrub: 3
});

// hills animation
scene1.to("#h1-1", { y: 3 * speed, x: 1 * speed, scale: 0.9, ease: "power1.in" }, 0);
scene1.to("#h1-2", { y: 2.6 * speed, x: -0.6 * speed, ease: "power1.in" }, 0);
scene1.to("#h1-3", { y: 1.7 * speed, x: 1.2 * speed }, 0.03);
scene1.to("#h1-4", { y: 3 * speed, x: 1 * speed }, 0.03);
scene1.to("#h1-5", { y: 2 * speed, x: 1 * speed }, 0.03);
scene1.to("#h1-6", { y: 2.3 * speed, x: -2.5 * speed }, 0);
scene1.to("#h1-7", { y: 5 * speed, x: 1.6 * speed }, 0);
scene1.to("#h1-8", { y: 3.5 * speed, x: 0.2 * speed }, 0);
scene1.to("#h1-9", { y: 3.5 * speed, x: -0.2 * speed }, 0);
scene1.to("#cloudsBig-L", { y: 4.5 * speed, x: -0.2 * speed }, 0);
scene1.to("#cloudsBig-R", { y: 4.5 * speed, x: -0.2 * speed }, 0);
scene1.to("#cloudStart-L", { x: -300 }, 0);
scene1.to("#cloudStart-R", { x: 300 }, 0);

//animate text
scene1.to("#info", { y: 8 * speed }, 0);

/* Bird */
gsap.fromTo(
    "#bird",
    { opacity: 1 },
    {
        y: -250,
        x: 800,
        ease: "power2.out",
        scrollTrigger: {
            trigger: ".scrollElement",
            start: "15% top",
            end: "60% 100%",
            scrub: 4,
            onEnter: function () {
                gsap.to("#bird", { scaleX: 1, rotation: 0 });
            },
            onLeave: function () {
                gsap.to("#bird", { scaleX: -1, rotation: -15 });
            }
        }
    }
);

/* Clouds */
let clouds = gsap.timeline();
ScrollTrigger.create({
    animation: clouds,
    trigger: ".scrollElement",
    start: "top top",
    end: "70% 100%",
    scrub: 1
});

clouds.to("#cloud1", { x: 500 }, 0);
clouds.to("#cloud2", { x: 1000 }, 0);
clouds.to("#cloud3", { x: -1000 }, 0);
clouds.to("#cloud4", { x: -700, y: 25 }, 0);

/* Brain motion Animation */
let brain = gsap.timeline();
ScrollTrigger.create({
    animation: brain,
    trigger: ".scrollElement",
    start: "1% top",
    end: "2150 100%",
    scrub: 2
});

// Brain motion and glow effects
brain.fromTo("#brain", { y: -50 }, { y: 230 }, 0);
brain.to("#brain", { filter: "url(#brainGlowStrong)" }, 0.5);
//bg change
brain.to("#bg_grad stop:nth-child(2)", { attr: { offset: "0.15" } }, 0);
brain.to("#bg_grad stop:nth-child(3)", { attr: { offset: "0.18" } }, 0);
brain.to("#bg_grad stop:nth-child(4)", { attr: { offset: "0.25" } }, 0);
brain.to("#bg_grad stop:nth-child(5)", { attr: { offset: "0.46" } }, 0);
brain.to("#bg_grad stop:nth-child(6)", { attr: { "stop-color": "#FF9171" } }, 0);

/* Brain Pulsing Animation */
gsap.to("#brain", {
    scale: 0.38,
    duration: 4,
    repeat: -1,
    yoyo: true,
    ease: "sine.inOut"
});

// Brain circuits pulsing
gsap.to("#brain .circuits", {
    opacity: 1,
    duration: 2.5,
    repeat: -1,
    yoyo: true,
    ease: "sine.inOut"
});

/* SCENE 2 */
let scene2 = gsap.timeline();
ScrollTrigger.create({
    animation: scene2,
    trigger: ".scrollElement",
    start: "15% top",
    end: "40% 100%",
    scrub: 3
});

scene2.fromTo("#h2-1", { y: 500, opacity: 0 }, { y: 0, opacity: 1 }, 0);
scene2.fromTo("#h2-2", { y: 500 }, { y: 0 }, 0.1);
scene2.fromTo("#h2-3", { y: 700 }, { y: 0 }, 0.1);
scene2.fromTo("#h2-4", { y: 700 }, { y: 0 }, 0.2);
scene2.fromTo("#h2-5", { y: 800 }, { y: 0 }, 0.3);
scene2.fromTo("#h2-6", { y: 900 }, { y: 0 }, 0.3);

/* Bats */
gsap.set("#bats", { transformOrigin: "50% 50%" });
gsap.fromTo(
    "#bats",
    { opacity: 1, y: 400, scale: 0 },
    {
        y: 20,
        scale: 0.8,
        transformOrigin: "50% 50%",
        ease: "power3.out",
        scrollTrigger: {
            trigger: ".scrollElement",
            start: "40% top",
            end: "70% 100%",
            scrub: 3,
            onEnter: function () {
                gsap.utils.toArray("#bats path").forEach((item, i) => {
                    gsap.to(item, {
                        scaleX: 0.5,
                        yoyo: true,
                        repeat: 9,
                        transformOrigin: "50% 50%",
                        duration: 0.15,
                        delay: 0.7 + i / 10
                    });
                });
                gsap.set("#bats", { opacity: 1 });
            },
            onLeave: function () {
                gsap.to("#bats", { opacity: 0, delay: 2 });
            }
        }
    }
);

/* Brain intensity increase and disintegration preparation */
let brain2 = gsap.timeline();
ScrollTrigger.create({
    animation: brain2,
    trigger: ".scrollElement",
    start: "2000 top",
    end: "5000 100%",
    scrub: 2
});

brain2.to("#brain", { scale: 0.45, filter: "url(#brainGlowStrong)" }, 0);
brain2.to("#bg_grad stop:nth-child(2)", { attr: { offset: "0.7" } }, 0);
brain2.to("#brain", { opacity: 0.8 }, 0.5);
brain2.to("#lg4 stop:nth-child(1)", { attr: { "stop-color": "#623951" } }, 0);
brain2.to("#lg4 stop:nth-child(2)", { attr: { "stop-color": "#261F36" } }, 0);
brain2.to("#bg_grad stop:nth-child(6)", { attr: { "stop-color": "#45224A" } }, 0);

/* Brain Disintegration Function */
function createBrainDisintegration() {
    const particlesContainer = document.getElementById('brainParticles');
    const particles = [];
    const particleCount = 50;

    // Clear any existing particles
    particlesContainer.innerHTML = '';

    // Create star-like particles
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElementNS("http://www.w3.org/2000/svg", "g");
        const size = Math.random() * 2 + 0.5;
        const startX = 375 + (Math.random() - 0.5) * 100; // Around brain center
        const startY = 150 + (Math.random() - 0.5) * 80;

        // Create a star shape
        const star = document.createElementNS("http://www.w3.org/2000/svg", "path");
        const starPath = `M${startX},${startY-size*2} L${startX+size*0.6},${startY-size*0.6} L${startX+size*2},${startY} L${startX+size*0.6},${startY+size*0.6} L${startX},${startY+size*2} L${startX-size*0.6},${startY+size*0.6} L${startX-size*2},${startY} L${startX-size*0.6},${startY-size*0.6} Z`;

        star.setAttribute("d", starPath);
        star.setAttribute("fill", "#2E86DE");
        star.setAttribute("opacity", "0.9");
        star.style.filter = "url(#brainGlow)";

        particle.appendChild(star);
        particlesContainer.appendChild(particle);

        particles.push({
            element: particle,
            star: star,
            startX: startX,
            startY: startY,
            velocityX: (Math.random() - 0.5) * 6,
            velocityY: (Math.random() - 0.5) * 6,
            size: size,
            rotation: Math.random() * 360
        });
    }

    return particles;
}

function animateBrainDisintegration() {
    const particles = createBrainDisintegration();

    // Show particles container
    gsap.set("#brainParticles", { opacity: 1 });

    // Hide the main brain
    gsap.to("#brain", { opacity: 0, duration: 0.5 });

    // Animate each particle
    particles.forEach((particle, index) => {
        const tl = gsap.timeline();
        const endX = particle.startX + particle.velocityX * 300;
        const endY = particle.startY + particle.velocityY * 300;

        // Create new star path for end position
        const endStarPath = `M${endX},${endY-particle.size*0.2} L${endX+particle.size*0.12},${endY-particle.size*0.12} L${endX+particle.size*0.2},${endY} L${endX+particle.size*0.12},${endY+particle.size*0.12} L${endX},${endY+particle.size*0.2} L${endX-particle.size*0.12},${endY+particle.size*0.12} L${endX-particle.size*0.2},${endY} L${endX-particle.size*0.12},${endY-particle.size*0.12} Z`;

        tl.to(particle.star, {
            attr: {
                d: endStarPath
            },
            opacity: 0,
            rotation: particle.rotation + 720,
            duration: 3,
            delay: index * 0.03,
            ease: "power2.out"
        });
    });

    // Hide particles container after animation
    gsap.to("#brainParticles", { opacity: 0, duration: 1, delay: 2 });
}

/* Transition (from Scene2 to Scene3) */
gsap.set("#scene3", { y: height - 40, visibility: "visible" });
let sceneTransition = gsap.timeline();
ScrollTrigger.create({
    animation: sceneTransition,
    trigger: ".scrollElement",
    start: "60% top",
    end: "bottom 100%",
    scrub: 3,
    onEnter: function() {
        // Trigger brain disintegration when entering scene 3
        setTimeout(() => {
            animateBrainDisintegration();
        }, 500);
    },
    onLeaveBack: function() {
        // Reset brain when going back
        gsap.set("#brain", { opacity: 1 });
        gsap.set("#brainParticles", { opacity: 0 });
        document.getElementById('brainParticles').innerHTML = '';
    }
});

sceneTransition.to("#h2-1", { y: -height - 100, scale: 1.5, transformOrigin: "50% 50%" }, 0);
sceneTransition.to("#bg_grad", { attr: { cy: "-80" } }, 0.0);
sceneTransition.to("#bg2", { y: 0 }, 0);

/* Enhanced Interactive Stars Animation */
function initStarAnimation() {
    const canvas = document.getElementById('starCanvas');
    const ctx = canvas.getContext('2d');
    let WIDTH = window.innerWidth;
    let HEIGHT = window.innerHeight;
    
    // Set canvas size
    canvas.width = WIDTH;
    canvas.height = HEIGHT;
    
    // Create stars
    const stars = [];
    const starCount = Math.floor(WIDTH * HEIGHT / 5000); // Density based on screen size
    for (let i = 0; i < starCount; i++) {
        stars.push({
            x: Math.random() * WIDTH,
            y: Math.random() * HEIGHT,
            r: Math.random() * 1.5 + 0.5,
            alpha: Math.random() * 0.5 + 0.1,
            speed: Math.random() * 0.05 + 0.05
        });
    }
    
    // Create dots for mouse interaction
    const dots = [];
    let mouseX = null;
    let mouseY = null;
    let mouseMoving = false;
    let mouseTimeout = null;
    
    // Mouse move event
    window.addEventListener('mousemove', function(e) {
        mouseX = e.clientX;
        mouseY = e.clientY;
        mouseMoving = true;
        
        // Clear any existing timeout
        if (mouseTimeout) clearTimeout(mouseTimeout);
        
        // Set timeout to reset mouseMoving flag
        mouseTimeout = setTimeout(() => {
            mouseMoving = false;
        }, 100);
        
        // Add new dot (limit to 20 dots)
        if (dots.length < 20) {
            dots.push({
                x: mouseX + (Math.random() * 40 - 20),
                y: mouseY + (Math.random() * 40 - 20),
                r: Math.random() * 3 + 1,
                alpha: 0.8, // More visible dots
                speed: Math.random() * 0.02 + 0.01,
                direction: Math.random() * 360
            });
        }
    });
    // Touch move event (for mobile)
window.addEventListener('touchmove', function(e) {
    if (e.touches.length > 0) {
        const touch = e.touches[0];
        mouseX = touch.clientX;
        mouseY = touch.clientY;
        mouseMoving = true;

        // Clear timeout
        if (mouseTimeout) clearTimeout(mouseTimeout);

        // Set timeout to reset movement flag
        mouseTimeout = setTimeout(() => {
            mouseMoving = false;
        }, 100);

        // Add new dot (limit to 20)
        if (dots.length < 20) {
            dots.push({
                x: mouseX + (Math.random() * 40 - 20),
                y: mouseY + (Math.random() * 40 - 20),
                r: Math.random() * 3 + 1,
                alpha: 0.8,
                speed: Math.random() * 0.02 + 0.01,
                direction: Math.random() * 360
            });
        }
    }
}, { passive: true }); // passive:true improves performance
    
    // Animation loop
    function animate() {
        ctx.clearRect(0, 0, WIDTH, HEIGHT);
        
        // Draw stars
        stars.forEach(star => {
            star.y -= star.speed;
            if (star.y < 0) star.y = HEIGHT;
            
            ctx.globalAlpha = star.alpha;
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(star.x, star.y, star.r, 0, Math.PI * 2);
            ctx.fill();
            
            // Twinkle effect
            if (Math.random() < 0.005) {
                star.alpha = Math.random() * 0.5 + 0.1;
            }
        });
        
        // Draw connecting dots if mouse is moving
        if (mouseMoving) {
            // Update dots
            dots.forEach((dot, i) => {
                dot.alpha -= 0.02; // Faster fade out
                dot.x += Math.cos(dot.direction * Math.PI / 180) * dot.speed * 10;
                dot.y += Math.sin(dot.direction * Math.PI / 180) * dot.speed * 10;
                
                if (dot.alpha <= 0) {
                    dots.splice(i, 1);
                }
            });
            
            // Draw dots
            dots.forEach(dot => {
                ctx.globalAlpha = dot.alpha;
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.beginPath();
                ctx.arc(dot.x, dot.y, dot.r, 0, Math.PI * 2);
                ctx.fill();
            });
            
            // Draw connections between dots
            if (dots.length > 1) {
                ctx.globalAlpha = 0.3;
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)';
                ctx.lineWidth = 0.8;
                
                for (let i = 0; i < dots.length; i++) {
                    for (let j = i + 1; j < dots.length; j++) {
                        const dx = dots[i].x - dots[j].x;
                        const dy = dots[i].y - dots[j].y;
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        
                        if (distance < 150) { // Increased connection distance
                            ctx.globalAlpha = (0.5 - distance/300) * dots[i].alpha * dots[j].alpha;
                            ctx.beginPath();
                            ctx.moveTo(dots[i].x, dots[i].y);
                            ctx.lineTo(dots[j].x, dots[j].y);
                            ctx.stroke();
                        }
                    }
                }
            }
            
            // Connect dots to mouse position if close
            if (mouseX !== null && mouseY !== null) {
                ctx.globalAlpha = 0.2;
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)';
                ctx.lineWidth = 0.5;
                
                dots.forEach(dot => {
                    const dx = dot.x - mouseX;
                    const dy = dot.y - mouseY;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < 200) {
                        ctx.globalAlpha = (0.5 - distance/400) * dot.alpha;
                        ctx.beginPath();
                        ctx.moveTo(dot.x, dot.y);
                        ctx.lineTo(mouseX, mouseY);
                        ctx.stroke();
                    }
                });
            }
        }
        
        requestAnimationFrame(animate);
    }
    
    // Handle window resize
    function handleResize() {
        WIDTH = window.innerWidth;
        HEIGHT = window.innerHeight;
        canvas.width = WIDTH;
        canvas.height = HEIGHT;
        
        // Adjust star density on resize
        const newStarCount = Math.floor(WIDTH * HEIGHT / 5000);
        while (stars.length < newStarCount) {
            stars.push({
                x: Math.random() * WIDTH,
                y: Math.random() * HEIGHT,
                r: Math.random() * 1.5 + 0.5,
                alpha: Math.random() * 0.5 + 0.1,
                speed: Math.random() * 0.05 + 0.05
            });
        }
        while (stars.length > newStarCount && stars.length > 0) {
            stars.pop();
        }
    }
    
    window.addEventListener('resize', handleResize);
    
    // Start animation
    animate();
}

// Initialize star animation on load but keep it hidden
window.addEventListener('load', function() {
    initStarAnimation();
    starCanvas.style.opacity = '0';
});

/* Scene 3 */
let scene3 = gsap.timeline();
ScrollTrigger.create({
    animation: scene3,
    trigger: ".scrollElement",
    start: "70% 50%",
    end: "bottom 100%",
    scrub: 3,
    onEnter: function() {
        // Show stars
        starCanvas.style.opacity = '1';
    },
    onLeaveBack: function() {
        // Hide stars
        starCanvas.style.opacity = '0';
    }
});

// Hills motion
scene3.fromTo("#h3-1", { y: 300 }, { y: -550 }, 0);
scene3.fromTo("#h3-2", { y: 800 }, { y: -550 }, 0.03);
scene3.fromTo("#h3-3", { y: 600 }, { y: -550 }, 0.06);
scene3.fromTo("#h3-4", { y: 800 }, { y: -550 }, 0.09);
scene3.fromTo("#h3-5", { y: 1000 }, { y: -550 }, 0.12);

// SVG stars animation
scene3.fromTo("#stars", { opacity: 0 }, { opacity: 0.5, y: -500 }, 0);

// Scroll Back text
scene3.fromTo("#arrow2", { opacity: 0 }, { opacity: 0.7, y: -710 }, 0.25);
scene3.fromTo("#text2", { opacity: 0 }, { opacity: 0.7, y: -710 }, 0.3);
scene3.to("footer", { opacity: 1 }, 0.3);

// Gradient value change
scene3.to("#bg2-grad", { attr: { cy: 600 } }, 0);
scene3.to("#bg2-grad", { attr: { r: 500 } }, 0);

/* Falling star */
gsap.set("#fstar", { y: -400 });
let fstarTL = gsap.timeline();
ScrollTrigger.create({
    animation: fstarTL,
    trigger: ".scrollElement",
    start: "4200 top",
    end: "6000 bottom",
    scrub: 2,
    onEnter: function () {
        gsap.set("#fstar", { opacity: 1 });
    },
    onLeave: function () {
        gsap.set("#fstar", { opacity: 0 });
    }
});
fstarTL.to("#fstar", { x: -700, y: -250, ease: "power2.out" }, 0);

// Twinkling stars
gsap.fromTo("#stars path", 
    { opacity: 0.1 }, 
    { 
        opacity: 0.8, 
        duration: 0.5, 
        repeat: -1, 
        yoyo: true,
        stagger: {
            each: 0.2,
            from: "random"
        }
    }
);

// Reset scrollbar position after refresh
window.onbeforeunload = function () {
    window.scrollTo(0, 0);
};
initStarAnimation();
starCanvas.classList.add('starCanvas-active');