<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brain Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #010112;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        svg {
            width: 100%;
            height: 100vh;
        }
        #brain {
            filter: drop-shadow(0 0 20px #E6E6FF);
            opacity: 0.8;
        }
        #brain .circuits {
            animation: circuitPulse 4s ease-in-out infinite alternate;
        }
        @keyframes circuitPulse {
            0% { opacity: 0.6; }
            100% { opacity: 0.9; }
        }
    </style>
</head>
<body>
    <svg viewBox="0 0 750 500">
        <defs>
            <!-- Brain Glow Effects -->
            <filter id="brainGlow" x="-50%" y="-50%" width="200%" height="200%">
              <feColorMatrix type="matrix" values="0 0 0 0 0.29 0 0 0 0 0.56 0 0 0 0 0.89 0 0 0 1 0"/>
              <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
              <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
        </defs>
        
        <!-- Brain SVG -->
        <g id="brain" transform="translate(375, 250) scale(0.5)" style="filter: url(#brainGlow)">
            <g fill="#E6E6FF" stroke="#D6D6FF" stroke-width="3" stroke-linejoin="round" stroke-linecap="round" opacity="0.9">
              <path stroke-width="10" style="stroke-dasharray:none;"
                d="m 474,194.1 c 1.2,-10.6 -0.5,-21.5 -4.9,-31.2 -6.4,-14.2 -18.5,-25.7 -33,-31.4 -1.9,-6.7 -5.7,-12.8 -10.9,-17.5 -7.3,-6.6 -17.2,-10.1 -27,-9.6 -3.5,-6.16 -8.6,-11.38 -14.7,-14.93 -7.9,-4.57 -17.3,-6.34 -26.3,-4.88 -9,1.45 -17.4,6.11 -23.4,12.96 -5.1,5.85 -8.3,13.05 -10,20.65 -1.7,7.5 -1.7,15.3 -1.7,23 l 1.3,224.6 c 0.1,17 2.3,33.9 3.7,50.8 1.3,15.9 0.7,32 -0.8,47.9 -0.8,8.7 -2,17.5 -2,26.3 0,8.7 1.1,17.7 4.7,25.7 5.2,11.9 15.5,21.1 27,27 22,11.3 49.4,11.3 71.5,0.2 19.8,-10 35.1,-28.5 41.1,-49.9 8.9,-3.1 16.9,-8.7 22.9,-15.9 5.9,-7.3 9.8,-16.3 11.1,-25.6 1,-7.5 0.3,-15.3 -2,-22.5 2.7,-3.9 5,-8.1 6.7,-12.5 5.3,-13.6 5,-29.3 -0.6,-42.8 9.4,-7.4 16.5,-17.7 19.9,-29.2 3.3,-11.5 3,-24 -0.9,-35.3 -4.4,-12.7 -13.4,-23.7 -25,-30.6 6.2,-17.3 5.6,-37 -1.6,-53.9 C 493.8,213 485,202 474,194.1 Z"/>
              
              <path opacity=".9" class="circuits" fill="#74B9FF"
                d="M429.6 253c.7-9 6.5-15.4 14.2-18 3.6-1 7.5-1.2 11.2-.8 7.6 1 15 4.5 20 10-2.8 2.5-5 5.6-6.3 9.2-3.3 11 2.4 22.2 12.7 25.7-8.7.4-14.7 8-13.4 15.7.7 4 3.2 7.2 4 11 1.4 13.6-12.6 20.4-23.5 16.7-9.4-3.6-16-13-17.5-21.7-8 12.6-25.4 21.7-36.8 17-8.8-4-9.8-13-12-20.8-3.7-14.3-19.4-9.7-25.4-3.5m5.6 84.3c-17-2.7-10.2-24 5-22m4-82.5c-4.2-11.2-21-9.2-23.2-.6-3 11.3 4.8 15 8.6 20.8 1.4 2 2.6 4.2 2.7 6.7-.6 9-11 11.5-15.6 17-6.5 8.7-.8 19.6 9.2 20.5"/>
            </g>
            <g transform="matrix(-1 0 0 1 640 0)">
              <g fill="#2E86DE" stroke="#54A0FF" stroke-width="4" stroke-linejoin="round" stroke-linecap="round">
                <path stroke-width="10" style="stroke-dasharray:none;"
                  d="m 474,194.1 c 1.2,-10.6 -0.5,-21.5 -4.9,-31.2 -6.4,-14.2 -18.5,-25.7 -33,-31.4 -1.9,-6.7 -5.7,-12.8 -10.9,-17.5 -7.3,-6.6 -17.2,-10.1 -27,-9.6 -3.5,-6.16 -8.6,-11.38 -14.7,-14.93 -7.9,-4.57 -17.3,-6.34 -26.3,-4.88 -9,1.45 -17.4,6.11 -23.4,12.96 -5.1,5.85 -8.3,13.05 -10,20.65 -1.7,7.5 -1.7,15.3 -1.7,23 l 1.3,224.6 c 0.1,17 2.3,33.9 3.7,50.8 1.3,15.9 0.7,32 -0.8,47.9 -0.8,8.7 -2,17.5 -2,26.3 0,8.7 1.1,17.7 4.7,25.7 5.2,11.9 15.5,21.1 27,27 22,11.3 49.4,11.3 71.5,0.2 19.8,-10 35.1,-28.5 41.1,-49.9 8.9,-3.1 16.9,-8.7 22.9,-15.9 5.9,-7.3 9.8,-16.3 11.1,-25.6 1,-7.5 0.3,-15.3 -2,-22.5 2.7,-3.9 5,-8.1 6.7,-12.5 5.3,-13.6 5,-29.3 -0.6,-42.8 9.4,-7.4 16.5,-17.7 19.9,-29.2 3.3,-11.5 3,-24 -0.9,-35.3 -4.4,-12.7 -13.4,-23.7 -25,-30.6 6.2,-17.3 5.6,-37 -1.6,-53.9 C 493.8,213 485,202 474,194.1 Z"/>
                
                <path opacity=".9" class="circuits" fill="#74B9FF"
                  d="M429.6 253c.7-9 6.5-15.4 14.2-18 3.6-1 7.5-1.2 11.2-.8 7.6 1 15 4.5 20 10-2.8 2.5-5 5.6-6.3 9.2-3.3 11 2.4 22.2 12.7 25.7-8.7.4-14.7 8-13.4 15.7.7 4 3.2 7.2 4 11 1.4 13.6-12.6 20.4-23.5 16.7-9.4-3.6-16-13-17.5-21.7-8 12.6-25.4 21.7-36.8 17-8.8-4-9.8-13-12-20.8-3.7-14.3-19.4-9.7-25.4-3.5m5.6 84.3c-17-2.7-10.2-24 5-22m4-82.5c-4.2-11.2-21-9.2-23.2-.6-3 11.3 4.8 15 8.6 20.8 1.4 2 2.6 4.2 2.7 6.7-.6 9-11 11.5-15.6 17-6.5 8.7-.8 19.6 9.2 20.5"/>
              </g>
            </g>
        </g>
    </svg>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script>
        // Simple pulsing animation
        gsap.to("#brain", {
            scale: 0.55,
            duration: 4,
            repeat: -1,
            yoyo: true,
            ease: "sine.inOut"
        });
    </script>
</body>
</html>
