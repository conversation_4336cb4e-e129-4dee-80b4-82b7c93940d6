# Brain Image Replacement Guide

This guide explains how to easily replace the brain SVG with a different image while maintaining all animations and effects.

## Quick Replacement Steps

### Option 1: Replace with SVG Image
1. Open `index.html`
2. Find the section with `id="brainImage"` (around line 290)
3. Replace the entire `<g id="brainImage">` content with your new SVG
4. Make sure to keep the same structure with individual pieces for the disintegration effect

### Option 2: Replace with Raster Image (PNG/JPG)
1. Place your image file in the project directory
2. Replace the brain SVG section with:
```html
<g id="brainImage" transform="translate(-320, -300)">
  <image href="your-image.png" width="640" height="600" opacity="0.8"/>
</g>
```

## Important Notes

### Positioning
- The brain is centered at coordinates (375, 250)
- The `brainImage` group is offset by (-320, -300) to center the content
- Adjust the transform values if your image has different dimensions

### Colors and Theming
- Current colors match the sky background: `#E6E6FF`, `#D6D6FF`, `#F0F0FF`
- The brain has 80% opacity (`opacity: 0.8`)
- Glow effects use sky-themed colors in the filters

### Disintegration Effect
- For proper disintegration, your image should have multiple pieces with IDs: `brainPiece1`, `brainPiece2`, etc.
- Each piece will animate separately when transitioning to scene 3
- If using a single image, the disintegration will create star particles around it

### Animation Properties
- Scale range: 0.4 to 0.48 (adjustable in `app.js`)
- Pulsing duration: 5 seconds
- Vertical movement: -100px to +50px during scroll
- Glow intensity increases during scene transitions

## File Locations

- **HTML Structure**: `index.html` (lines 287-324)
- **Animations**: `app.js` (brain-related functions)
- **Styling**: `style.css` (brain glow effects)
- **Test File**: `test-brain.html` (for testing changes)

## Customization Options

### Adjust Size
Change the scale value in the brain transform:
```html
<g id="brain" transform="translate(375, 250) scale(0.4)">
```

### Modify Colors
Update the fill and stroke attributes:
```html
fill="#YOUR_COLOR" stroke="#YOUR_STROKE_COLOR"
```

### Change Opacity
Modify the opacity in the brain group:
```html
style="filter: url(#brainGlow); opacity: 0.8;"
```

### Adjust Glow Effect
Edit the filter definitions in the `<defs>` section to change glow colors and intensity.

## Testing Your Changes

1. Open `test-brain.html` in a browser to see the brain in isolation
2. Open `index.html` to see the full animation sequence
3. Scroll through the page to test all animation phases
4. Check the disintegration effect when transitioning to scene 3

## Troubleshooting

- **Brain not visible**: Check the positioning and scale values
- **No glow effect**: Ensure the filter is properly applied
- **Disintegration not working**: Verify piece IDs match the JavaScript expectations
- **Wrong colors**: Update the fill/stroke attributes and filter definitions
