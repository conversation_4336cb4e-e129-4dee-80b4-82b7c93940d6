# Neural Network Replacement Guide

This guide explains how to easily replace the 3D neural network with a different visualization while maintaining all animations and effects.

## Quick Replacement Steps

### Option 1: Replace with Different 3D Visualization
1. Open `app.js`
2. Find the `initializeGraph()` function (around line 130)
3. Replace the ForceGraph3D initialization with your preferred 3D library
4. Keep the same container element `#3d-graph`

### Option 2: Replace with 2D Visualization
1. Open `index.html`
2. Find the foreignObject with `id="brain"` (around line 287)
3. Replace the content with your 2D visualization:
```html
<foreignObject id="brain" x="275" y="150" width="200" height="200" style="opacity: 0.8;">
  <div id="your-visualization" style="width: 200px; height: 200px;"></div>
</foreignObject>
```

### Option 3: Replace with Static Image
1. Replace the foreignObject with an SVG image:
```html
<image id="brain" x="275" y="150" width="200" height="200" href="your-image.png" style="opacity: 0.8;"/>
```

## Important Notes

### Positioning
- The neural network is centered horizontally at x=275 (for 200px width in 750px SVG)
- Vertical position at y=150
- Container size is 200x200 pixels
- Adjust x position if changing width: x = (750 - width) / 2

### Colors and Theming
- Current colors match the sky background: `#E6E6FF`, `#D6D6FF`, `#F0F0FF`
- The neural network has 80% opacity (`opacity: 0.8`)
- Node colors, link colors, and particle colors are customizable in the JavaScript

### Disintegration Effect
- Creates 50 star particles and 15 glowing orbs
- Particles spread out in all directions when transitioning to scene 3
- Animation duration: 4.5 seconds
- Particles fade out while rotating and scaling down

### Animation Properties
- Scale range: 1.0 to 1.3 (adjustable in `app.js`)
- Pulsing duration: 4 seconds
- Vertical movement: -100px to +50px during scroll
- 3D rotation: continuous at 0.3 degrees per frame
- Click interaction: regenerates neural connections

## File Locations

- **HTML Structure**: `index.html` (lines 287-290)
- **3D Graph Logic**: `app.js` (neural network functions)
- **Styling**: `style.css` (3D graph container styles)
- **Test File**: `test-neural-network.html` (for testing changes)
- **Dependencies**: Three.js and 3d-force-graph libraries

## Customization Options

### Adjust Size
Change the width/height in the foreignObject:
```html
<foreignObject id="brain" x="275" y="150" width="200" height="200">
```

### Modify Node/Link Properties
Update the graph configuration in `app.js`:
```javascript
Graph.nodeRelSize(4);           // Node size
Graph.nodeColor(() => '#E6E6FF'); // Node color
Graph.linkWidth(3);             // Link thickness
Graph.linkColor(() => '#D6D6FF'); // Link color
```

### Change Network Density
Modify the node count and connection logic:
```javascript
const N = 300; // Number of nodes
let numNodeLinks = Math.round(Math.random() * (0.75 + Math.random())) + 1; // Connections per node
```

### Adjust Rotation Speed
Change the rotation increment:
```javascript
currentAngle += 0.3; // Degrees per frame
```

### Modify Bloom Effect
Adjust the bloom pass parameters:
```javascript
bloomPass.strength = 2;    // Glow intensity
bloomPass.radius = 0.8;    // Glow spread
bloomPass.threshold = 0.3; // Glow threshold
```

## Testing Your Changes

1. Open `test-neural-network.html` to see the 3D graph in isolation
2. Open `index.html` to see the full animation sequence
3. Scroll through the page to test all animation phases
4. Click the neural network to test connection regeneration
5. Check the disintegration effect when transitioning to scene 3

## Troubleshooting

- **Neural network not visible**: Check browser console for Three.js errors
- **No rotation**: Verify the rotation interval is running
- **No bloom effect**: Ensure UnrealBloomPass is properly imported
- **Click not working**: Check event listeners are attached
- **Performance issues**: Reduce node count or disable bloom effect
