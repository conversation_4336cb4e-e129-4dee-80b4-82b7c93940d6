<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neural Network Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000011;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            color: #DDD;
            font-family: Helvetica;
        }
        #container {
            text-align: center;
        }
        #3d-graph {
            width: 400px;
            height: 400px;
            border: 1px solid #333;
            border-radius: 50%;
            overflow: hidden;
            margin: 20px auto;
        }
        #3d-graph canvas {
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div id="container">
        <h1>3D Neural Network Test</h1>
        <div id="3d-graph"></div>
        <p>Click to regenerate connections</p>
    </div>
    
    <script src="//unpkg.com/three"></script>
    <script src="//unpkg.com/3d-force-graph"></script>
    <script type="module">
        import { UnrealBloomPass } from '//unpkg.com/three@0.123.0/examples/jsm/postprocessing/UnrealBloomPass.js';
        
        const deg2rad = deg => { return deg * Math.PI / 180; }
        
        const N = 300;
        const nodes = [...Array(N).keys()].map(i => {
            return { 
                id: i,
                val: (Math.random() * 1.5) + 1
            };
        });

        function generateLinks(nodes) {
            let links = [];
            nodes.forEach(node => {
                let numNodeLinks = Math.round(Math.random() * (0.75 + Math.random())) + 1;
                for(let i = 0; i < numNodeLinks; i++) {
                    links.push({
                        source: node.id,
                        target: Math.round(Math.random() * (node.id > 0 ? node.id - 1 : node.id))
                    });
                }
            });
            return links;
        }
        
        const links = generateLinks(nodes);
        const gData = {nodes, links};

        const distance = 600;
        const graphElem = document.getElementById("3d-graph");

        const Graph = ForceGraph3D()(graphElem);
        Graph.enableNodeDrag(false);
        Graph.enableNavigationControls(false);
        Graph.enablePointerInteraction(false);
        Graph.showNavInfo(false);

        Graph.cameraPosition({ z: distance });

        Graph.nodeRelSize(4);
        Graph.nodeOpacity(1);
        Graph.nodeColor(() => '#E6E6FF');

        Graph.linkWidth(3);
        Graph.linkColor(() => '#D6D6FF');
        Graph.linkOpacity(0.6);

        Graph.linkDirectionalParticles(5);
        Graph.linkDirectionalParticleWidth(3);
        Graph.linkDirectionalParticleColor(() => '#F0F0FF');

        const bloomPass = new UnrealBloomPass();
        bloomPass.strength = 2;
        bloomPass.radius = 1;
        bloomPass.threshold = 0.3;
        Graph.postProcessingComposer().addPass(bloomPass);

        Graph.graphData(gData);

        let currentAngle = 0;
        setInterval(() => {
            Graph.cameraPosition({
                x: distance * Math.sin(deg2rad(currentAngle)),
                z: distance * Math.cos(deg2rad(currentAngle))
            });
            
            currentAngle += 0.5;
        }, 16);

        function onClick(e) {
            e.preventDefault();
            const newLinks = generateLinks(nodes);
            const newGData = {nodes, links: newLinks};
            Graph.graphData(newGData);
        }

        graphElem.addEventListener('click', onClick, false);
        graphElem.addEventListener('touchstart', onClick, false);
    </script>
</body>
</html>
